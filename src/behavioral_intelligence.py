"""
Jaeger ORB Timeframes - Opening Range Breakout focused timeframe generation.

ORB-FOCUSED ARCHITECTURE:
- backtesting.py: Handles OHLC resampling (superior, battle-tested)
- Our code: Adds ONLY Opening Range Breakout context
- Zero complexity: Focus exclusively on ORB patterns
"""

import pandas as pd
import numpy as np
from typing import Dict
from backtesting.lib import OHLCV_AGG


def generate_orb_timeframes(data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
    """
    ORB-FOCUSED timeframe generation: backtesting.py resampling + Opening Range Breakout context ONLY.

    ARCHITECTURE:
    1. Use backtesting.py's superior OHLCV_AGG for resampling (no manual code)
    2. Add ONLY Opening Range Breakout context - NO OTHER METRICS
    3. Focus exclusively on intraday ORB patterns

    Args:
        data: M1 OHLCV DataFrame with datetime index

    Returns:
        Dictionary of timeframe DataFrames with ORB context ONLY
    """
    print("📊 ORB-FOCUSED timeframe generation: backtesting.py + Opening Range Breakout context...")

    timeframes = {}

    # ORB-FOCUSED timeframe configurations - INTRADAY ONLY
    timeframe_configs = {
        '1min': '1min',    # 1-minute bars for precise ORB
        '5min': '5min',    # 5-minute bars
        '10min': '10min',  # 10-minute bars (NEW for ORB)
        '15min': '15min',  # 15-minute bars
        '30min': '30min',  # 30-minute bars
        '60min': '60min'   # 60-minute bars (1-hour)
    }

    # ARCHITECTURAL FIX: Ensure data has required columns (proper capitalization)
    # Data comes from backtesting.py with proper capitalization: Open, High, Low, Close, Volume
    required_cols = ['Open', 'High', 'Low', 'Close']
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        raise ValueError(f"Data missing required columns: {missing_cols}")

    # Add Volume if missing
    if 'Volume' not in data.columns:
        data = data.copy()
        data['Volume'] = np.nan

    print(f"   📊 Input data: {len(data)} M1 bars from {data.index[0]} to {data.index[-1]}")

    # Generate each timeframe using ORB-FOCUSED architecture
    for tf_name, freq in timeframe_configs.items():
        try:
            print(f"   🔄 {tf_name}: backtesting.py resampling + ORB context...")

            # CLEAN: Use backtesting.py's superior OHLCV aggregation (zero manual code)
            clean_resampled = data.resample(freq, label='right').agg(OHLCV_AGG).dropna()

            if clean_resampled.empty:
                print(f"   ⚠️  {tf_name} resulted in empty data - skipping")
                continue

            # ORB-FOCUSED: Add Opening Range Breakout context
            orb_data = add_orb_context(clean_resampled, tf_name)

            timeframes[tf_name] = orb_data
            print(f"   ✅ {tf_name}: {len(orb_data)} bars with ORB context")

        except Exception as e:
            print(f"   ❌ Failed to generate {tf_name}: {e}")
            continue

    print(f"✅ ORB timeframe generation complete: {len(timeframes)} timeframes")
    print("   🚀 backtesting.py: Superior OHLC resampling")
    print("   🎯 Our code: Opening Range Breakout context")
    return timeframes


def add_orb_context(data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    Add ONLY Opening Range Breakout context to clean backtesting.py resampled data.

    ORB-FOCUSED ARCHITECTURE:
    - Input: Clean OHLC data from backtesting.py's superior resampling
    - Output: Data with Opening Range Breakout context
    - Zero complexity: ORB-relevant metrics

    Args:
        data: Clean OHLC DataFrame from backtesting.py
        timeframe: Timeframe identifier (e.g., '5min', '15min')

    Returns:
        DataFrame with ORB context ONLY
    """
    orb_data = data.copy()

    print(f"   🎯 Adding ORB context for {timeframe}")

    try:
        # Basic time context for ORB
        orb_data['hour'] = orb_data.index.hour
        orb_data['minute'] = orb_data.index.minute
        orb_data['timeframe'] = timeframe

        # Basic directional context (needed for ORB)
        orb_data['bullish_bar'] = orb_data['Close'] > orb_data['Open']
        orb_data['bearish_bar'] = orb_data['Close'] < orb_data['Open']

        # ORB-specific metrics
        orb_data['bar_range'] = orb_data['High'] - orb_data['Low']
        orb_data['is_opening_session'] = (orb_data['hour'] >= 8) & (orb_data['hour'] <= 10)  # London/NY opening hours

        # Simple breakout detection (price above/below previous high/low)
        orb_data['above_prev_high'] = orb_data['Close'] > orb_data['High'].shift(1)
        orb_data['below_prev_low'] = orb_data['Close'] < orb_data['Low'].shift(1)

        # ORB-specific opening range calculation (flexible candle count)
        # Opening range can be based on 1st, 2nd, 3rd, or more candles - whatever works
        orb_data['date'] = orb_data.index.date

        # Calculate session candle numbers for candles_since_session_start condition
        orb_data['session_candle_number'] = orb_data.groupby('date').cumcount() + 1

        # Calculate multiple opening range periods for flexibility
        daily_opening_ranges = {}
        for date in orb_data['date'].unique():
            day_data = orb_data[orb_data['date'] == date]
            if len(day_data) > 0:
                # Generate multiple opening range options (1-6 bars)
                max_bars = min(6, len(day_data))  # Up to 6 bars for opening range

                # Calculate ranges for different bar counts
                ranges = {}
                for bar_count in range(1, max_bars + 1):
                    opening_data = day_data.head(bar_count)
                    ranges[f'{bar_count}_bar'] = {
                        'high': opening_data['High'].max(),
                        'low': opening_data['Low'].min()
                    }

                # Use default opening range (configurable, default to 2 bars)
                default_bars = min(2, len(day_data))
                opening_data = day_data.head(default_bars)
                daily_opening_ranges[date] = {
                    'high': opening_data['High'].max(),
                    'low': opening_data['Low'].min(),
                    'ranges': ranges  # Store all range options
                }

        # Add opening range levels to data
        orb_data['opening_range_high'] = orb_data['date'].map(lambda d: daily_opening_ranges.get(d, {}).get('high', np.nan))
        orb_data['opening_range_low'] = orb_data['date'].map(lambda d: daily_opening_ranges.get(d, {}).get('low', np.nan))

        # ORB breakout signals
        orb_data['orb_breakout_up'] = orb_data['Close'] > orb_data['opening_range_high']
        orb_data['orb_breakout_down'] = orb_data['Close'] < orb_data['opening_range_low']
        orb_data['orb_breakout'] = orb_data['orb_breakout_up'] | orb_data['orb_breakout_down']

        # Clean up temporary date column
        orb_data = orb_data.drop('date', axis=1)

        # Clean up any infinite or NaN values
        orb_data = orb_data.replace([np.inf, -np.inf], np.nan)
        orb_data = orb_data.ffill().fillna(0)

        print(f"   ✅ ORB context added: {len(orb_data)} bars with opening range breakout signals")
        return orb_data

    except Exception as e:
        print(f"   ⚠️  Error adding ORB context to {timeframe}: {e}")
        # Return basic data if ORB enhancement fails
        orb_data['hour'] = orb_data.index.hour if isinstance(orb_data.index, pd.DatetimeIndex) else 0
        orb_data['timeframe'] = timeframe
        orb_data['bullish_bar'] = orb_data['Close'] > orb_data['Open']
        orb_data['bearish_bar'] = orb_data['Close'] < orb_data['Open']
        orb_data['bar_range'] = orb_data['High'] - orb_data['Low']
        orb_data['is_opening_session'] = False
        orb_data['above_prev_high'] = False
        orb_data['below_prev_low'] = False
        orb_data['opening_range_high'] = np.nan
        orb_data['opening_range_low'] = np.nan
        orb_data['orb_breakout_up'] = False
        orb_data['orb_breakout_down'] = False
        orb_data['orb_breakout'] = False
        orb_data['session_candle_number'] = 1  # Default to first candle
        return orb_data


def generate_orb_summaries(timeframe_data: Dict[str, pd.DataFrame]) -> str:
    """
    Generate CONCISE ORB summaries for LLM pattern discovery.
    Focused on Opening Range Breakout patterns - NO behavioral metrics.
    """
    summaries = []

    for tf_name, df in timeframe_data.items():
        if df.empty:
            continue

        try:
            # ORB-specific metrics
            total_bars = len(df)
            bullish_bars = df['bullish_bar'].sum() if 'bullish_bar' in df.columns else 0
            orb_breakouts = df['orb_breakout'].sum() if 'orb_breakout' in df.columns else 0
            orb_up_breakouts = df['orb_breakout_up'].sum() if 'orb_breakout_up' in df.columns else 0
            orb_down_breakouts = df['orb_breakout_down'].sum() if 'orb_breakout_down' in df.columns else 0

            # Calculate percentages
            bullish_pct = (bullish_bars / total_bars * 100) if total_bars > 0 else 0
            orb_breakout_pct = (orb_breakouts / total_bars * 100) if total_bars > 0 else 0

            # Most active trading hours
            most_active_hour = df.groupby('hour').size().idxmax() if len(df) > 0 and 'hour' in df.columns else 0

            # ORB-focused summary
            summary = f"{tf_name.upper()}: {total_bars} bars | Bullish: {bullish_pct:.0f}% | ORB Breakouts: {orb_breakouts} ({orb_breakout_pct:.1f}%) | Up: {orb_up_breakouts} | Down: {orb_down_breakouts} | Peak Hour: {most_active_hour}"

            summaries.append(summary)

        except Exception as e:
            print(f"   ⚠️  Error generating ORB summary for {tf_name}: {e}")
            summaries.append(f"{tf_name.upper()}: ORB Error - {e}")

    return " | ".join(summaries)


# ORB-Focused Usage:
"""
# Load M1 data
data = pd.read_csv('your_m1_data.csv', index_col=0, parse_dates=True)

# Generate ORB-focused timeframes (1m, 5m, 10m, 15m, 30m, 60m)
timeframes = generate_orb_timeframes(data)

# Generate ORB summaries (NO behavioral metrics)
summaries = generate_orb_summaries(timeframes)
print(summaries)
"""


# REMOVED: Legacy aliases violate ZERO FALLBACKS principle
# All code must use the explicit ORB functions:
# - generate_orb_timeframes() instead of generate_clean_timeframes()
# - generate_orb_summaries() instead of generate_behavioral_summaries()
