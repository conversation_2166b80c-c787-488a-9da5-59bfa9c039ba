#!/usr/bin/env python3
"""
🎯 PATTERN TRANSLATION PROMPTS - STAGE 2 OF TWO-STAGE DISCOVERY SYSTEM

This module implements Stage 2: Translation of sophisticated patterns discovered in Stage 1
into backtesting-compatible JSON schema format that can be parsed and validated by the existing pipeline.

Stage 1: Tom <PERSON> → Stage 2: JSON Schema Translation → Validation Pipeline
"""

import json

def get_session_for_symbol(symbol: str) -> str:
    """
    Determine the correct trading session based on instrument type.

    CFD Trading Session Rules:
    - FTSE/DAX (European indices and their stocks): London session
    - DOW/NASDAQ (US indices and their stocks): NY session
    - Forex pairs: ALL sessions

    Args:
        symbol: Trading symbol (e.g., 'GBRIDXGBP', 'DAX', 'EURUSD')

    Returns:
        Session value: 'london', 'ny', or 'all'
    """
    symbol_upper = symbol.upper()

    # European indices (FTSE, DAX) and their corresponding stocks
    # GBRIDXGBP IS FTSE (not FTSE-related)
    if any(x in symbol_upper for x in ['FTSE', 'GBR', 'DAX', 'DEU', 'UK100', 'GER40', 'GBRIDX']):
        return 'london'

    # US indices (DOW, NASDAQ) and their corresponding stocks
    elif any(x in symbol_upper for x in ['DOW', 'NASDAQ', 'US30', 'NAS100', 'DJI', 'IXIC']):
        return 'ny'

    # Forex pairs (contain currency codes)
    elif any(x in symbol_upper for x in ['USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD']):
        # Check if it's a forex pair (6+ characters with currency codes)
        if len(symbol) >= 6:
            return 'all'
        else:
            # Single currency or short symbol, default to all
            return 'all'

    # Default for unknown instruments
    else:
        return 'all'

class PatternTranslationPrompts:
    """Generate prompts to translate sophisticated patterns into backtesting-compatible format"""
    
    @staticmethod
    def get_backtesting_constraints():
        """Get the exact constraints required for ORB-focused JSON schema compatibility"""
        return {
            "entry_conditions": [
                "orb_breakout_above", "orb_breakout_below", "opening_range_high", "opening_range_low",
                "close_above_orb_high", "close_below_orb_low", "candles_since_session_start", "session_filter",
                "orb_range_size", "orb_time_filter", "multi_timeframe_orb_confirm"
            ],
            "exit_conditions": [
                "risk_reward_ratio", "fixed_stop_loss", "fixed_take_profit", "trailing_stop",
                "time_exit", "orb_failure", "session_end", "orb_target_reached"
            ],
            "entry_logic_options": ["AND", "OR"],
            "position_sizing_methods": ["fixed_percent", "fixed_amount", "volatility_based", "kelly_criterion"],
            "timeframe_options": ["1m", "5m", "15m", "1h", "4h", "1d", "1w"],
            "market_regimes": ["trending", "ranging", "volatile", "quiet", "bullish", "bearish"],
            "sessions": ["asian", "london", "new_york", "overlap"]
        }
    
    @staticmethod
    def get_translation_principles():
        """Core principles for translating sophisticated CFD profit patterns to simple rules"""
        return [
            "Preserve the core PROFITABILITY insight while simplifying execution logic for CFD trading",
            "Convert complex multi-condition patterns into the most essential PROFIT-GENERATING trigger condition",
            "Maintain the statistical PROFIT edge by focusing on the primary profit-driving pattern element",
            "Ensure the simplified pattern still exploits the same behavioral inefficiency for MAXIMUM PROFITS",
            "Choose the backtesting format that best captures the pattern's PROFIT-GENERATING essence",
            "Prioritize patterns that can be effectively simplified without losing their PROFIT edge",
            "Optimize for CFD leverage (1:100) to maximize profit potential in the simplified format",
            "If a pattern cannot be simplified without losing its core PROFITABILITY logic, note this limitation"
        ]
    
    @staticmethod
    def get_translation_examples():
        """Examples of ORB pattern translation to JSON schema format"""
        return [
            {
                "sophisticated": "When London session opens and price breaks above the first 30-minute range high with strong momentum, institutional flows typically drive price higher as retail participants chase the breakout",
                "json_output": {
                    "pattern_name": "London 30-Minute ORB Breakout",
                    "description": "Exploits institutional momentum and retail FOMO after London opening range breakout",
                    "market_situation": "London session opening with clear 30-minute range establishment followed by upward breakout",
                    "entry_conditions": [
                        {
                            "condition": "orb_breakout_above",
                            "orb_period_minutes": 30,
                            "orb_period_bars": 2
                        },
                        {
                            "condition": "candles_since_session_start",
                            "value": 10
                        },
                        {
                            "condition": "session_filter",
                            "value": "london"
                        }
                    ],
                    "entry_logic": "AND",
                    "exit_conditions": [
                        {
                            "condition": "risk_reward_ratio",
                            "risk": 1,
                            "reward": 3
                        }
                    ],
                    "optimal_conditions": {
                        "timeframe": "15min",
                        "session": "london"
                    },
                    "orb_logic": "London opening creates institutional order flow that establishes clear range, breakout indicates continuation of institutional direction"
                }
            },
            {
                "sophisticated": "During New York session, when price breaks below the first 60-minute range low, institutional selling pressure typically accelerates as stop losses are triggered",
                "json_output": {
                    "pattern_name": "NY 60-Minute ORB Breakdown",
                    "description": "Exploits institutional selling and stop-loss cascades after NY opening range breakdown",
                    "market_situation": "New York session with established 60-minute range followed by downward breakdown",
                    "entry_conditions": [
                        {
                            "condition": "orb_breakout_below",
                            "orb_period_minutes": 60,
                            "orb_period_bars": 4,
                            "direction": "short"
                        },
                        {
                            "condition": "candles_since_session_start",
                            "value": 15
                        },
                        {
                            "condition": "session_filter",
                            "value": "ny"
                        }
                    ],
                    "exit_conditions": [
                        {
                            "condition": "risk_reward_ratio",
                            "risk": 1,
                            "reward": 2
                        }
                    ],
                    "optimal_conditions": {
                        "timeframe": "30min",
                        "session": "new_york"
                    },
                    "orb_logic": "NY opening range breakdown triggers institutional selling and retail stop-loss cascades"
                }
            }
        ]
    
    @staticmethod
    def generate_stage2_translation_prompt(sophisticated_patterns, symbol=None):
        """
        Generate Stage 2: Pattern Translation Prompt

        This prompt takes sophisticated patterns from Stage 1 and translates them
        into backtesting-compatible format while preserving their core insights.

        Args:
            sophisticated_patterns: The output from Stage 1 discovery
            symbol: Trading symbol for session determination (e.g., 'GBRIDXGBP')

        Returns:
            Stage 2 translation prompt string
        """
        
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        principles = PatternTranslationPrompts.get_translation_principles()
        examples = PatternTranslationPrompts.get_translation_examples()

        # Determine correct session based on symbol
        recommended_session = get_session_for_symbol(symbol) if symbol else 'all'
        session_guidance = f"""
🎯 SYMBOL-SPECIFIC SESSION GUIDANCE FOR {symbol or 'UNKNOWN'}:
Recommended session filter: "{recommended_session}"

CFD Trading Session Rules:
• FTSE/DAX (and their stocks): Use "london" session
• Dow/Nasdaq (and their stocks): Use "ny" session
• Forex pairs: Use "all" sessions
• Unknown instruments: Use "all" sessions

CRITICAL: Use session_filter with "value": "{recommended_session}" in your patterns.
"""
        
        prompt = f"""🎯 STAGE 2: CFD PATTERN TRANSLATION TO BACKTESTING FORMAT FOR MAXIMUM PROFITABILITY

You have successfully discovered sophisticated CFD trading patterns using Tom Hougaard's methodology. Now you must translate these PROFIT-FOCUSED patterns into backtesting-compatible format while preserving their core PROFITABILITY insights.

💰 CRITICAL: These patterns are designed for CFD trading with 1:100 leverage. Every translation decision must prioritize MAXIMUM PROFITABILITY.

📊 SOPHISTICATED CFD PROFIT PATTERNS DISCOVERED IN STAGE 1:
{sophisticated_patterns}

🔧 CFD PROFITABILITY TRANSLATION MISSION:
Convert each sophisticated CFD profit pattern above into simple backtesting-compatible rules that can be tested and validated for MAXIMUM PROFIT GENERATION. Your goal is to preserve the core PROFITABILITY insight while simplifying the execution logic for optimal CFD trading results.

🎯 TRANSLATION PRINCIPLES:
{chr(10).join([f'• {principle}' for principle in principles])}

{session_guidance}

🚨 JSON SCHEMA CONSTRAINTS - MANDATORY FORMAT:

**Entry Condition Types - Use ONLY these:**
{chr(10).join([f'• {condition}' for condition in constraints['entry_conditions']])}

**Exit Condition Types - Use ONLY these:**
{chr(10).join([f'• {condition}' for condition in constraints['exit_conditions']])}

**Entry Logic Options:** {', '.join(constraints['entry_logic_options'])}
**Position Sizing Methods:** {', '.join(constraints['position_sizing_methods'])}
**Timeframe Options:** {', '.join(constraints['timeframe_options'])}
**Market Regimes:** {', '.join(constraints['market_regimes'])}
**Sessions:** {', '.join(constraints['sessions'])}

🌟 JSON TRANSLATION EXAMPLES:

Example 1:
Sophisticated Pattern: "{examples[0]['sophisticated']}"
JSON Output:
```json
{json.dumps(examples[0]['json_output'], indent=2)}
```

Example 2:
Sophisticated Pattern: "{examples[1]['sophisticated']}"
JSON Output:
```json
{json.dumps(examples[1]['json_output'], indent=2)}
```

🎯 FLEXIBLE OPENING RANGE BREAKOUT GUIDANCE:

**CRITICAL**: Opening Range Breakouts can be based on ANY number of candles:
• 1st candle only (orb_period_bars: 1)
• First 2 candles (orb_period_bars: 2)
• First 3 candles (orb_period_bars: 3)
• Up to 6 candles (orb_period_bars: 6)

**No limitations**: If a pattern works with the 4th or 5th candle forming the range, use it!
**Profitability first**: Choose the bar count that makes the pattern most profitable.

🎯 REQUIRED ORB TRANSLATION OUTPUT FORMAT - JSON SCHEMA:

You MUST output valid JSON using the ORB-focused schema structure. For each ORB pattern from Stage 1, provide a JSON object:

```json
{{
  "pattern_name": "[ORB Pattern Name from Stage 1]",
  "description": "[Detailed description of the ORB pattern and why it works]",
  "market_situation": "[Specific session/time conditions where this ORB pattern occurs]",
  "entry_conditions": [
    {{
      "condition": "[ORB_condition_type from approved list]",
      "orb_period_minutes": "[30, 60, 90, etc.]",
      "orb_period_bars": "[1, 2, 3, 4, 5, 6 - flexible candle count for opening range]",
      "session": "[london, ny, asian, overlap, all]",
      "direction": "[long, short, both]"
    }}
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {{
      "condition": "risk_reward_ratio",
      "risk": 1,
      "reward": "[reward_multiplier_minimum_2]"
    }}
  ],
  "position_sizing": {{
    "method": "fixed_percent",
    "value": "[percentage_as_decimal]",
    "max_risk": "[max_risk_as_decimal]"
  }},
  "optimal_conditions": {{
    "timeframe": "[CHOOSE_ONE: 1min, 5min, 10min, 15min, 30min, 60min]",
    "session": "[CHOOSE_ONE: london, new_york, asian, london_ny_overlap]"
  }},
  "orb_logic": "[Why this specific ORB pattern works - institutional behavior, retail psychology, etc.]"
}}
```

🚨 CRITICAL ORB JSON TRANSLATION REQUIREMENTS:

• Output MUST be valid JSON that can be parsed by json.loads()
• Use ONLY the ORB condition types from the approved lists above
• Include ALL required fields: pattern_name, entry_conditions, exit_conditions
• Focus EXCLUSIVELY on Opening Range Breakout patterns - NO other pattern types
• Preserve the core ORB logic in the orb_logic field
• Ensure each ORB pattern has a clear statistical edge
• Use appropriate ORB periods (15-240 minutes) and session parameters
• Specify exact timeframes that work best for each ORB pattern

🎯 ORB PROFITABILITY REQUIREMENTS:
• Profit target MUST be at least 2:1 risk-reward minimum (preferably 3:1)
• Pattern must overcome 1-pip spread cost PLUS generate substantial profits
• Focus ONLY on HIGH-PROBABILITY ORB setups with clear institutional flow
• Expected win rate >60% OR risk-reward ratio >2:1

🚨 CRITICAL TIMEFRAME REQUIREMENT:
• You MUST choose exactly ONE optimal timeframe for each pattern (not multiple options)
• Choose the SINGLE BEST timeframe that maximizes the pattern's profitability
• Consider: 1min (high frequency), 5min (scalping), 15min (swing), 30min (position), 60min (trend)
• Your choice determines the backtesting timeframe - choose wisely for maximum profits

Begin translating your ORB patterns into backtesting-compatible format now:"""

        return prompt
    
    @staticmethod
    def validate_translation_output(translation_output):
        """
        Validate that the translation output meets backtesting requirements
        
        Args:
            translation_output: The LLM's translation output
            
        Returns:
            Dict with validation results
        """
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check for required entry conditions
        valid_entry_found = False
        for entry_condition in constraints['entry_conditions']:
            if entry_condition in translation_output.lower():
                valid_entry_found = True
                break

        if not valid_entry_found:
            validation_results['warnings'].append("No recognized entry condition found")

        # Check for required exit conditions
        valid_exit_found = False
        for exit_condition in constraints['exit_conditions']:
            if exit_condition in translation_output.lower():
                valid_exit_found = True
                break

        if not valid_exit_found:
            validation_results['warnings'].append("No recognized exit condition found")

        # Check for position sizing method
        sizing_found = False
        for sizing_method in constraints['position_sizing_methods']:
            if sizing_method in translation_output.lower():
                sizing_found = True
                break

        if not sizing_found:
            validation_results['warnings'].append("No recognized position sizing method found")
        
        return validation_results
