#!/usr/bin/env python3
"""
Debug script to test ORB pattern matching logic and identify why patterns generate zero signals.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add src to path
sys.path.append('src')

from behavioral_intelligence import add_orb_context
from backtesting_rule_parser import BacktestingRuleParser, TradingPattern

def load_ftse_data(max_rows=10000):
    """Load and prepare FTSE data with ORB context"""
    print(f"Loading FTSE data (max {max_rows} rows)...")
    
    data = pd.read_csv('data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    # Use subset for testing
    data = data.head(max_rows)
    
    # Add ORB context
    orb_data = add_orb_context(data, '1min')
    
    print(f"Data prepared: {len(orb_data)} bars")
    print(f"Date range: {orb_data.index.min()} to {orb_data.index.max()}")
    
    return orb_data

def test_pattern_conditions(data, pattern_def):
    """Test individual pattern conditions"""
    print(f"\n=== Testing Pattern: {pattern_def['pattern_name']} ===")
    
    parser = BacktestingRuleParser()
    
    # Enable debug logging
    parser._debug_logging = True
    
    total_signals = 0
    condition_stats = {}
    
    # Test each condition individually first
    for i, condition in enumerate(pattern_def['entry_conditions']):
        condition_type = condition['condition']
        condition_stats[condition_type] = {'passed': 0, 'failed': 0, 'errors': 0}
        
        print(f"\n--- Testing Condition {i+1}: {condition_type} ---")
        
        for idx in range(1, min(1000, len(data))):  # Test first 1000 bars
            try:
                result = parser._evaluate_condition(condition, data, idx)
                if result:
                    condition_stats[condition_type]['passed'] += 1
                else:
                    condition_stats[condition_type]['failed'] += 1
            except Exception as e:
                condition_stats[condition_type]['errors'] += 1
                if condition_stats[condition_type]['errors'] <= 3:  # Show first 3 errors
                    print(f"  Error at idx {idx}: {e}")
    
    # Print condition statistics
    print(f"\n--- Condition Statistics ---")
    for condition_type, stats in condition_stats.items():
        total_tested = stats['passed'] + stats['failed'] + stats['errors']
        if total_tested > 0:
            pass_rate = stats['passed'] / total_tested * 100
            print(f"{condition_type}: {stats['passed']}/{total_tested} passed ({pass_rate:.1f}%), {stats['errors']} errors")
    
    # Now test combined pattern logic
    print(f"\n--- Testing Combined Pattern Logic ---")
    try:
        # Create TradingPattern object
        trading_pattern = TradingPattern(
            pattern_name=pattern_def['pattern_name'],
            entry_conditions=pattern_def['entry_conditions'],
            entry_logic=pattern_def.get('entry_logic', 'AND'),
            exit_conditions=[{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}],
            position_sizing={"method": "fixed_percent", "value": 0.01},
            optimal_conditions={"timeframes": ["1min"]}
        )

        pattern_function = parser._create_python_function(trading_pattern)

        for idx in range(1, min(5000, len(data))):  # Test more bars for combined logic
            try:
                signal = pattern_function(data, idx)
                if signal is not None:
                    total_signals += 1
                    if total_signals <= 5:  # Show first 5 signals
                        print(f"  Signal {total_signals} at idx {idx}: {signal}")
            except Exception as e:
                print(f"  Pattern error at idx {idx}: {e}")
                break

    except Exception as e:
        print(f"Failed to create pattern function: {e}")
        return 0
    
    print(f"\nTotal signals generated: {total_signals}")
    return total_signals

def main():
    """Main debug function"""
    print("🔍 ORB Pattern Debug Script")
    print("=" * 50)
    
    # Load data
    data = load_ftse_data(max_rows=20000)
    
    # Define the failing patterns from the LLM output
    failing_patterns = [
        {
            "pattern_name": "London Rush",
            "entry_conditions": [
                {
                    "condition": "orb_breakout_above",
                    "orb_period_minutes": 30,
                    "orb_period_bars": 2
                },
                {
                    "condition": "candles_since_session_start",
                    "value": 10
                },
                {
                    "condition": "session_filter",
                    "value": "london"
                }
            ],
            "entry_logic": "AND"
        },
        {
            "pattern_name": "Asian Gap",
            "entry_conditions": [
                {
                    "condition": "orb_breakout_above",
                    "orb_period_minutes": 60,
                    "orb_period_bars": 6
                },
                {
                    "condition": "candles_since_session_start",
                    "value": 15
                },
                {
                    "condition": "session_filter",
                    "value": "london"
                }
            ],
            "entry_logic": "AND"
        },
        {
            "pattern_name": "New York Squeeze",
            "entry_conditions": [
                {
                    "condition": "orb_breakout_below",
                    "orb_period_minutes": 60,
                    "orb_period_bars": 3
                },
                {
                    "condition": "candles_since_session_start",
                    "value": 15
                },
                {
                    "condition": "session_filter",
                    "value": "ny"
                }
            ],
            "entry_logic": "AND"
        }
    ]
    
    # Working pattern for comparison
    working_pattern = {
        "pattern_name": "Range Expansion",
        "entry_conditions": [
            {
                "condition": "orb_breakout_above",
                "orb_period_minutes": 30,
                "orb_period_bars": 1
            },
            {
                "condition": "candles_since_session_start",
                "value": 10
            }
        ],
        "entry_logic": "AND"
    }
    
    # Test all patterns
    results = {}
    
    # Test failing patterns
    for pattern in failing_patterns:
        signals = test_pattern_conditions(data, pattern)
        results[pattern['pattern_name']] = signals
    
    # Test working pattern
    signals = test_pattern_conditions(data, working_pattern)
    results[working_pattern['pattern_name']] = signals
    
    # Summary
    print("\n" + "=" * 50)
    print("🎯 PATTERN SIGNAL SUMMARY")
    print("=" * 50)
    for pattern_name, signal_count in results.items():
        status = "✅ WORKING" if signal_count > 0 else "❌ BROKEN"
        print(f"{pattern_name}: {signal_count} signals {status}")

if __name__ == "__main__":
    main()
